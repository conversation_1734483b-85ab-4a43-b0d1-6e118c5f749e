{ config, lib, namespace, ... }:

{
  killtw = {
    suites = {
      common.enable = true;
      development.enable = true;
    };

    user = {
      enable = true;
      name = "killtw";
      fullName = "<PERSON> Li";
      email = "<EMAIL>";
    };

    programs.cloud.colima = {
      enable = true;
      useHead = true;
      autoStart = true;
      cpu = 4;
      memory = 12;
      disk = 60;
      networkAddress = true;
      networkMode = "bridged";
      dns = [ "*******" "*******" ];
    };
  };

  home.stateVersion = "24.05";
}
